#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频压缩工具安装脚本
自动检查和安装所需依赖
"""

import subprocess
import sys
import os
import platform

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✓ Python版本检查通过: {sys.version}")
    return True

def install_pip_packages():
    """安装Python包"""
    print("\n正在安装Python依赖包...")
    
    packages = [
        "ffmpeg-python==0.2.0",
        "Pillow==10.0.0"
    ]
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {package} 安装失败: {e}")
            return False
    
    return True

def check_ffmpeg():
    """检查FFmpeg是否安装"""
    print("\n检查FFmpeg...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ FFmpeg已安装并可用")
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            print(f"  版本: {version_line}")
            return True
        else:
            print("✗ FFmpeg不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("✗ FFmpeg未找到")
        return False

def show_ffmpeg_install_instructions():
    """显示FFmpeg安装说明"""
    system = platform.system().lower()
    
    print("\n" + "="*50)
    print("FFmpeg安装说明")
    print("="*50)
    
    if system == "windows":
        print("Windows系统:")
        print("1. 访问 https://ffmpeg.org/download.html")
        print("2. 下载Windows版本的FFmpeg")
        print("3. 解压到任意目录（如 C:\\ffmpeg）")
        print("4. 将bin目录添加到系统PATH环境变量")
        print("5. 重启命令提示符或PowerShell")
        print("\n或者使用包管理器:")
        print("- Chocolatey: choco install ffmpeg")
        print("- Scoop: scoop install ffmpeg")
        
    elif system == "darwin":  # macOS
        print("macOS系统:")
        print("使用Homebrew安装:")
        print("brew install ffmpeg")
        print("\n如果没有Homebrew，先安装:")
        print('/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"')
        
    elif system == "linux":
        print("Linux系统:")
        print("Ubuntu/Debian: sudo apt update && sudo apt install ffmpeg")
        print("CentOS/RHEL: sudo yum install ffmpeg")
        print("Fedora: sudo dnf install ffmpeg")
        print("Arch Linux: sudo pacman -S ffmpeg")
    
    print("\n安装完成后，重新运行此脚本进行验证。")

def create_desktop_shortcut():
    """创建桌面快捷方式（Windows）"""
    if platform.system().lower() != "windows":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "视频压缩工具.lnk")
        target = os.path.join(os.getcwd(), "video_compressor.py")
        wDir = os.getcwd()
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = sys.executable
        shortcut.save()
        
        print("✓ 桌面快捷方式创建成功")
    except ImportError:
        print("提示: 可以手动创建快捷方式到桌面")
    except Exception as e:
        print(f"创建快捷方式失败: {e}")

def main():
    """主安装流程"""
    print("视频压缩工具 - 安装向导")
    print("="*40)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装Python包
    if not install_pip_packages():
        print("\n安装失败: Python包安装出错")
        return False
    
    # 检查FFmpeg
    ffmpeg_ok = check_ffmpeg()
    
    if not ffmpeg_ok:
        show_ffmpeg_install_instructions()
        print("\n警告: FFmpeg未安装，程序无法正常工作")
        print("请按照上述说明安装FFmpeg后再运行程序")
        return False
    
    # 创建快捷方式
    create_desktop_shortcut()
    
    print("\n" + "="*40)
    print("✓ 安装完成!")
    print("现在可以运行以下命令启动程序:")
    print("python video_compressor.py")
    print("="*40)
    
    return True

if __name__ == "__main__":
    success = main()
    
    input("\n按回车键退出...")
    
    if success:
        # 询问是否立即启动程序
        choice = input("是否立即启动视频压缩工具? (y/n): ").lower()
        if choice in ['y', 'yes', '是']:
            try:
                subprocess.run([sys.executable, "video_compressor.py"])
            except Exception as e:
                print(f"启动程序失败: {e}")
