@echo off
chcp 65001 >nul
title 视频压缩工具

echo 视频压缩工具启动器
echo ==================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或未添加到PATH
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查是否首次运行
if not exist "requirements.txt" (
    echo 错误: 找不到requirements.txt文件
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

REM 检查依赖是否安装
python -c "import ffmpeg" >nul 2>&1
if errorlevel 1 (
    echo 检测到首次运行，正在安装依赖...
    python install.py
    if errorlevel 1 (
        echo 安装失败，请手动运行: python install.py
        pause
        exit /b 1
    )
)

REM 启动程序
echo 启动视频压缩工具...
python video_compressor.py

if errorlevel 1 (
    echo 程序运行出错
    pause
)
