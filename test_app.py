#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频压缩工具测试脚本
测试程序的基本功能和依赖
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def test_python_version():
    """测试Python版本"""
    print("测试Python版本...")
    if sys.version_info >= (3, 7):
        print(f"✓ Python版本: {sys.version}")
        return True
    else:
        print(f"✗ Python版本过低: {sys.version}")
        return False

def test_tkinter():
    """测试tkinter"""
    print("测试tkinter...")
    try:
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("✓ tkinter可用")
        return True
    except Exception as e:
        print(f"✗ tkinter不可用: {e}")
        return False

def test_ffmpeg_python():
    """测试ffmpeg-python包"""
    print("测试ffmpeg-python包...")
    try:
        import ffmpeg
        print("✓ ffmpeg-python包已安装")
        return True
    except ImportError:
        print("✗ ffmpeg-python包未安装")
        return False

def test_ffmpeg_binary():
    """测试FFmpeg二进制文件"""
    print("测试FFmpeg二进制文件...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✓ FFmpeg可用: {version_line}")
            return True
        else:
            print("✗ FFmpeg不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("✗ FFmpeg未找到")
        return False

def test_video_compressor_import():
    """测试主程序导入"""
    print("测试主程序导入...")
    try:
        # 临时添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 尝试导入主程序类
        from video_compressor import VideoCompressor
        print("✓ 主程序可以正常导入")
        return True
    except Exception as e:
        print(f"✗ 主程序导入失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    print("测试GUI创建...")
    try:
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 导入并创建应用实例
        from video_compressor import VideoCompressor
        app = VideoCompressor(root)
        
        # 立即销毁窗口
        root.destroy()
        print("✓ GUI可以正常创建")
        return True
    except Exception as e:
        print(f"✗ GUI创建失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("视频压缩工具 - 功能测试")
    print("=" * 40)
    
    tests = [
        ("Python版本", test_python_version),
        ("tkinter库", test_tkinter),
        ("ffmpeg-python包", test_ffmpeg_python),
        ("FFmpeg二进制", test_ffmpeg_binary),
        ("主程序导入", test_video_compressor_import),
        ("GUI创建", test_gui_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
        print()
    
    # 汇总结果
    print("=" * 40)
    print("测试结果汇总:")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print("=" * 40)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序可以正常运行。")
        return True
    else:
        print("⚠️  部分测试失败，程序可能无法正常工作。")
        print("请根据上述错误信息解决问题。")
        return False

def main():
    """主函数"""
    success = run_all_tests()
    
    print("\n" + "=" * 40)
    if success:
        print("测试完成！可以运行以下命令启动程序:")
        print("python video_compressor.py")
    else:
        print("请解决上述问题后重新测试。")
        print("如需帮助，请运行: python install.py")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
