# 视频压缩工具

一个简单易用的视频压缩工具，具有图形化界面。

## 功能特点

- 🎬 支持多种视频格式（MP4, AVI, MOV, MKV等）
- 🎛️ 可调节压缩质量、分辨率和码率
- 📊 实时显示压缩进度
- 🖥️ 现代化的图形界面
- ⚡ 基于FFmpeg的高效压缩

## 安装要求

1. Python 3.7+
2. FFmpeg（需要安装到系统PATH中）

## 安装步骤

1. 克隆或下载项目
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 确保FFmpeg已安装并在系统PATH中
4. 运行程序：
   ```bash
   python video_compressor.py
   ```

## 使用说明

1. 点击"选择视频文件"选择要压缩的视频
2. 设置输出路径
3. 调整压缩参数（质量、分辨率等）
4. 点击"开始压缩"
5. 等待压缩完成

## 注意事项

- 首次使用需要确保FFmpeg正确安装
- 压缩时间取决于视频大小和设置的质量
- 建议在压缩前备份原始视频文件
- 支持中文文件名和路径
- 如遇到编码问题，请查看"修复说明.md"

## 版本更新

### v1.1.0 (最新版)
- 🔧 修复了Windows系统下的编码问题
- 🔧 改进了进度监控的稳定性
- 🔧 添加了备用进度显示方案
- 🔧 优化了中文文件名支持

### v1.0.0
- ✨ 基础视频压缩功能
- ✨ 图形化用户界面
