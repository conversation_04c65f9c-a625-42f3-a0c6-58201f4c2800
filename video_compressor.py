#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频压缩工具 - 图形界面版本
使用FFmpeg进行视频压缩，提供友好的GUI界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import subprocess
import ffmpeg
from pathlib import Path
import time

class VideoCompressor:
    def __init__(self, root):
        self.root = root
        self.root.title("视频压缩工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 变量
        self.input_file = tk.StringVar()
        self.output_file = tk.StringVar()
        self.quality = tk.StringVar(value="23")  # CRF值，越小质量越高
        self.resolution = tk.StringVar(value="原始")
        self.bitrate = tk.StringVar(value="自动")
        self.is_compressing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="视频压缩工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        self.create_file_selection_area(main_frame)
        
        # 压缩设置区域
        self.create_compression_settings_area(main_frame)
        
        # 进度显示区域
        self.create_progress_area(main_frame)
        
        # 控制按钮区域
        self.create_control_buttons_area(main_frame)
        
    def create_file_selection_area(self, parent):
        """创建文件选择区域"""
        # 文件选择框架
        file_frame = ttk.LabelFrame(parent, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 输入文件
        ttk.Label(file_frame, text="输入视频:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Entry(file_frame, textvariable=self.input_file, width=50).grid(
            row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(0, 5))
        ttk.Button(file_frame, text="浏览", 
                  command=self.select_input_file).grid(row=0, column=2, pady=(0, 5))
        
        # 输出文件
        ttk.Label(file_frame, text="输出路径:").grid(row=1, column=0, sticky=tk.W)
        ttk.Entry(file_frame, textvariable=self.output_file, width=50).grid(
            row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        ttk.Button(file_frame, text="浏览", 
                  command=self.select_output_file).grid(row=1, column=2)
        
    def create_compression_settings_area(self, parent):
        """创建压缩设置区域"""
        # 设置框架
        settings_frame = ttk.LabelFrame(parent, text="压缩设置", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(1, weight=1)
        
        # 质量设置
        ttk.Label(settings_frame, text="压缩质量:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        quality_frame = ttk.Frame(settings_frame)
        quality_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(0, 5))
        
        self.quality_scale = ttk.Scale(quality_frame, from_=18, to=35, 
                                      variable=self.quality, orient=tk.HORIZONTAL)
        self.quality_scale.grid(row=0, column=0, sticky=(tk.W, tk.E))
        quality_frame.columnconfigure(0, weight=1)
        
        self.quality_label = ttk.Label(quality_frame, text="CRF: 23 (推荐)")
        self.quality_label.grid(row=0, column=1, padx=(10, 0))
        
        # 绑定质量滑块事件
        self.quality_scale.configure(command=self.update_quality_label)
        
        # 分辨率设置
        ttk.Label(settings_frame, text="输出分辨率:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        resolution_combo = ttk.Combobox(settings_frame, textvariable=self.resolution,
                                       values=["原始", "1920x1080", "1280x720", "854x480", "640x360"],
                                       state="readonly", width=20)
        resolution_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 5))
        
        # 码率设置
        ttk.Label(settings_frame, text="目标码率:").grid(row=2, column=0, sticky=tk.W)
        bitrate_combo = ttk.Combobox(settings_frame, textvariable=self.bitrate,
                                    values=["自动", "500k", "1000k", "2000k", "5000k", "10000k"],
                                    width=20)
        bitrate_combo.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
    def create_progress_area(self, parent):
        """创建进度显示区域"""
        # 进度框架
        progress_frame = ttk.LabelFrame(parent, text="压缩进度", padding="10")
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                           maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 状态标签
        self.status_label = ttk.Label(progress_frame, text="准备就绪")
        self.status_label.grid(row=1, column=0, sticky=tk.W)
        
        # 日志文本框
        log_frame = ttk.Frame(progress_frame)
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
    def create_control_buttons_area(self, parent):
        """创建控制按钮区域"""
        # 按钮框架
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(10, 0))
        
        # 开始压缩按钮
        self.compress_button = ttk.Button(button_frame, text="开始压缩", 
                                         command=self.start_compression)
        self.compress_button.grid(row=0, column=0, padx=(0, 10))
        
        # 停止按钮
        self.stop_button = ttk.Button(button_frame, text="停止", 
                                     command=self.stop_compression, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        # 清除日志按钮
        ttk.Button(button_frame, text="清除日志",
                  command=self.clear_log).grid(row=0, column=2)

    def select_input_file(self):
        """选择输入视频文件"""
        filetypes = [
            ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v"),
            ("MP4文件", "*.mp4"),
            ("AVI文件", "*.avi"),
            ("MOV文件", "*.mov"),
            ("所有文件", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="选择要压缩的视频文件",
            filetypes=filetypes
        )

        if filename:
            self.input_file.set(filename)
            # 自动设置输出文件名
            self.auto_set_output_file(filename)
            self.log_message(f"已选择输入文件: {filename}")

    def select_output_file(self):
        """选择输出文件路径"""
        if not self.input_file.get():
            messagebox.showwarning("警告", "请先选择输入视频文件")
            return

        # 获取输入文件的目录和基本名称
        input_path = Path(self.input_file.get())
        default_name = f"{input_path.stem}_compressed{input_path.suffix}"

        filename = filedialog.asksaveasfilename(
            title="选择输出文件路径",
            defaultextension=".mp4",
            initialfilename=default_name,
            filetypes=[
                ("MP4文件", "*.mp4"),
                ("AVI文件", "*.avi"),
                ("MOV文件", "*.mov"),
                ("MKV文件", "*.mkv"),
                ("所有文件", "*.*")
            ]
        )

        if filename:
            self.output_file.set(filename)
            self.log_message(f"已设置输出路径: {filename}")

    def auto_set_output_file(self, input_filename):
        """根据输入文件自动设置输出文件名"""
        input_path = Path(input_filename)

        # 检查文件名是否已经包含_compressed，避免重复添加
        stem = input_path.stem
        if stem.endswith('_compressed'):
            # 如果已经是压缩文件，移除_compressed后缀，然后重新添加
            original_stem = stem[:-11]  # 移除'_compressed'
            output_filename = input_path.parent / f"{original_stem}_compressed_v2.mp4"
        else:
            output_filename = input_path.parent / f"{stem}_compressed.mp4"

        self.output_file.set(str(output_filename))

    def update_quality_label(self, value):
        """更新质量标签显示"""
        crf_value = int(float(value))
        if crf_value <= 20:
            quality_desc = "极高质量"
        elif crf_value <= 23:
            quality_desc = "高质量(推荐)"
        elif crf_value <= 28:
            quality_desc = "中等质量"
        else:
            quality_desc = "低质量"

        self.quality_label.config(text=f"CRF: {crf_value} ({quality_desc})")

    def log_message(self, message):
        """在日志区域添加消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清除日志内容"""
        self.log_text.delete(1.0, tk.END)

    def update_status(self, message):
        """更新状态标签"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def validate_inputs(self):
        """验证输入参数"""
        if not self.input_file.get():
            messagebox.showerror("错误", "请选择输入视频文件")
            return False

        if not os.path.exists(self.input_file.get()):
            messagebox.showerror("错误", "输入文件不存在")
            return False

        if not self.output_file.get():
            messagebox.showerror("错误", "请设置输出文件路径")
            return False

        # 检查输出目录是否存在
        output_dir = os.path.dirname(self.output_file.get())
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                messagebox.showerror("错误", f"无法创建输出目录: {str(e)}")
                return False

        # 验证输入文件是否为有效的视频文件
        if not self.is_valid_video_file(self.input_file.get()):
            return False

        return True

    def is_valid_video_file(self, file_path):
        """验证文件是否为有效的视频文件"""
        try:
            # 使用ffprobe检查文件
            cmd = ['ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
                   '-show_entries', 'stream=codec_type', '-of', 'csv=p=0', file_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and 'video' in result.stdout:
                return True
            else:
                messagebox.showerror("错误", "选择的文件不是有效的视频文件")
                return False

        except subprocess.TimeoutExpired:
            messagebox.showerror("错误", "文件验证超时，文件可能损坏")
            return False
        except Exception as e:
            messagebox.showwarning("警告", f"无法验证文件格式: {str(e)}\n将尝试继续处理")
            return True  # 如果验证失败，仍然允许尝试处理

    def get_compression_params(self):
        """获取压缩参数"""
        params = {
            'crf': int(float(self.quality.get())),
            'resolution': self.resolution.get(),
            'bitrate': self.bitrate.get()
        }
        return params

    def check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(['ffmpeg', '-version'],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False

    def start_compression(self):
        """开始压缩"""
        if self.is_compressing:
            messagebox.showwarning("警告", "压缩正在进行中")
            return

        if not self.validate_inputs():
            return

        if not self.check_ffmpeg():
            messagebox.showerror("错误",
                               "FFmpeg未找到或不可用。请确保FFmpeg已安装并添加到系统PATH中。")
            return

        # 禁用开始按钮，启用停止按钮
        self.compress_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.is_compressing = True

        # 重置进度
        self.progress_var.set(0)
        self.update_status("正在准备压缩...")

        # 在新线程中执行压缩
        self.compression_thread = threading.Thread(target=self.compress_video)
        self.compression_thread.daemon = True
        self.compression_thread.start()

    def stop_compression(self):
        """停止压缩"""
        if hasattr(self, 'ffmpeg_process') and self.ffmpeg_process:
            try:
                self.ffmpeg_process.terminate()
                self.log_message("用户取消了压缩操作")
            except:
                pass

        self.is_compressing = False
        self.compress_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.update_status("压缩已停止")

    def compress_video(self):
        """执行视频压缩的核心方法"""
        try:
            input_file = self.input_file.get()
            output_file = self.output_file.get()
            params = self.get_compression_params()

            # 验证输入文件
            if not os.path.exists(input_file):
                raise FileNotFoundError(f"输入文件不存在: {input_file}")

            input_size = os.path.getsize(input_file)
            self.log_message(f"开始压缩: {os.path.basename(input_file)} ({input_size/1024/1024:.1f} MB)")
            self.log_message(f"输出文件: {os.path.basename(output_file)}")
            self.log_message(f"压缩参数: CRF={params['crf']}, 分辨率={params['resolution']}, 码率={params['bitrate']}")

            # 检查输入文件是否可读
            try:
                with open(input_file, 'rb') as f:
                    f.read(1024)  # 尝试读取前1KB
            except Exception as e:
                raise Exception(f"无法读取输入文件: {str(e)}")

            # 构建FFmpeg命令
            cmd = self.build_ffmpeg_command(input_file, output_file, params)
            self.log_message(f"FFmpeg命令: {' '.join(cmd)}")

            # 获取视频总时长用于计算进度
            duration = self.get_video_duration(input_file)

            # 执行FFmpeg命令
            self.ffmpeg_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )

            # 监控进度
            if duration > 0:
                self.monitor_progress(duration)
            else:
                # 如果无法获取时长，使用简单的状态更新
                self.update_status("压缩中...（无法显示进度）")

            # 等待完成
            return_code = self.ffmpeg_process.wait()

            if return_code == 0 and self.is_compressing:
                self.progress_var.set(100)
                self.update_status("压缩完成!")
                self.log_message("压缩成功完成!")

                # 验证输出文件是否创建成功
                if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                    # 显示文件大小对比
                    self.show_compression_result(input_file, output_file)
                    messagebox.showinfo("完成", "视频压缩完成!")
                else:
                    self.log_message("警告: 输出文件未正确创建")
                    self.update_status("压缩可能失败")
                    messagebox.showwarning("警告", "压缩完成但输出文件异常，请检查")

            elif self.is_compressing:
                # 提供更详细的错误信息
                error_msg = self.get_ffmpeg_error_message(return_code)
                self.log_message(f"FFmpeg返回错误代码: {return_code} - {error_msg}")
                self.update_status("压缩失败")

                # 检查是否是文件被占用的问题
                if return_code == 1 or "Permission denied" in error_msg:
                    messagebox.showerror("错误", "压缩失败：文件可能被其他程序占用，请关闭相关程序后重试")
                else:
                    messagebox.showerror("错误", f"压缩失败：{error_msg}\n\n请查看日志获取详细信息")

        except Exception as e:
            self.log_message(f"压缩过程中发生异常: {str(e)}")
            self.update_status("压缩失败")
            messagebox.showerror("错误", f"压缩失败: {str(e)}")

        finally:
            # 重置UI状态
            self.is_compressing = False
            self.compress_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.ffmpeg_process = None

    def build_ffmpeg_command(self, input_file, output_file, params):
        """构建FFmpeg命令"""
        cmd = ['ffmpeg', '-i', input_file, '-y']  # -y 覆盖输出文件

        # 视频编码设置
        cmd.extend(['-c:v', 'libx264'])
        cmd.extend(['-crf', str(params['crf'])])
        cmd.extend(['-preset', 'medium'])  # 编码速度预设

        # 分辨率设置
        if params['resolution'] != '原始':
            cmd.extend(['-s', params['resolution']])

        # 码率设置
        if params['bitrate'] != '自动':
            cmd.extend(['-b:v', params['bitrate']])

        # 音频编码设置
        cmd.extend(['-c:a', 'aac'])
        cmd.extend(['-b:a', '128k'])

        # 其他优化设置
        cmd.extend(['-movflags', '+faststart'])  # 优化网络播放
        cmd.extend(['-pix_fmt', 'yuv420p'])     # 兼容性设置

        # 进度报告 - 使用stderr避免编码问题
        cmd.extend(['-progress', 'pipe:1'])
        cmd.extend(['-stats'])  # 显示统计信息

        cmd.append(output_file)

        return cmd

    def get_video_duration(self, input_file):
        """获取视频时长（秒）"""
        try:
            # 首先尝试使用ffmpeg-python
            probe = ffmpeg.probe(input_file)

            # 查找视频流
            video_stream = None
            for stream in probe['streams']:
                if stream['codec_type'] == 'video':
                    video_stream = stream
                    break

            if video_stream and 'duration' in video_stream:
                duration = float(video_stream['duration'])
                self.log_message(f"视频时长: {duration:.1f}秒")
                return duration

            # 如果视频流没有duration，尝试从format中获取
            if 'format' in probe and 'duration' in probe['format']:
                duration = float(probe['format']['duration'])
                self.log_message(f"视频时长: {duration:.1f}秒")
                return duration

        except Exception as e:
            self.log_message(f"ffmpeg-python获取时长失败: {str(e)}")

        # 备用方法：直接使用ffprobe命令
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                '-of', 'csv=p=0', input_file
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                duration = float(result.stdout.strip())
                self.log_message(f"视频时长: {duration:.1f}秒")
                return duration
        except Exception as e:
            self.log_message(f"ffprobe命令获取时长失败: {str(e)}")

        self.log_message("无法获取视频时长，将使用简化进度显示")
        return 0

    def monitor_progress(self, total_duration):
        """监控压缩进度"""
        if not self.ffmpeg_process or total_duration <= 0:
            return

        try:
            progress_count = 0
            for line in iter(self.ffmpeg_process.stdout.readline, ''):
                if not self.is_compressing:
                    break

                try:
                    line = line.strip()
                    if not line:
                        continue

                    if line.startswith('out_time_ms='):
                        # 解析当前处理时间
                        time_str = line.split('=')[1]
                        if time_str.isdigit():
                            time_ms = int(time_str)
                            current_time = time_ms / 1000000.0  # 转换为秒

                            # 计算进度百分比
                            progress = min((current_time / total_duration) * 100, 100)
                            self.progress_var.set(progress)

                            # 更新状态
                            self.update_status(f"压缩中... {progress:.1f}%")

                    elif line.startswith('speed='):
                        # 显示处理速度（每10次更新一次，避免日志过多）
                        progress_count += 1
                        if progress_count % 10 == 0:
                            speed_info = line.split('=')[1].strip()
                            self.log_message(f"处理速度: {speed_info}")

                except (ValueError, IndexError, UnicodeDecodeError) as e:
                    # 忽略单行解析错误，继续处理下一行
                    continue

        except Exception as e:
            self.log_message(f"进度监控错误: {str(e)}")
            # 如果进度监控失败，使用简单的状态更新
            self.simple_progress_monitor()

    def simple_progress_monitor(self):
        """简化的进度监控（当详细监控失败时使用）"""
        self.update_status("压缩中...（无法显示详细进度）")
        progress = 0
        while self.ffmpeg_process and self.ffmpeg_process.poll() is None and self.is_compressing:
            progress = min(progress + 2, 90)  # 缓慢增加到90%
            self.progress_var.set(progress)
            time.sleep(1)  # 每秒更新一次

    def show_compression_result(self, input_file, output_file):
        """显示压缩结果对比"""
        try:
            input_size = os.path.getsize(input_file)
            output_size = os.path.getsize(output_file)

            compression_ratio = (1 - output_size / input_size) * 100

            input_size_mb = input_size / (1024 * 1024)
            output_size_mb = output_size / (1024 * 1024)

            result_msg = (
                f"压缩完成!\n"
                f"原始大小: {input_size_mb:.1f} MB\n"
                f"压缩后大小: {output_size_mb:.1f} MB\n"
                f"压缩率: {compression_ratio:.1f}%"
            )

            self.log_message(result_msg.replace('\n', ' | '))

        except Exception as e:
            self.log_message(f"无法计算压缩结果: {str(e)}")

    def get_ffmpeg_error_message(self, return_code):
        """根据FFmpeg返回代码提供错误说明"""
        # 将返回代码转换为有符号整数（处理大数值）
        if return_code > 2147483647:
            return_code = return_code - 4294967296

        error_messages = {
            1: "一般错误（文件不存在、权限问题等）",
            2: "参数错误",
            -1: "程序被中断",
            -2: "内存不足",
            -22: "参数无效",
            -32: "管道错误",
            -104: "连接重置",
        }

        if return_code in error_messages:
            return error_messages[return_code]
        elif return_code < 0:
            return f"系统错误 (代码: {return_code})"
        else:
            return f"未知错误 (代码: {return_code})"


def main():
    """主函数"""
    root = tk.Tk()
    app = VideoCompressor(root)

    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件
        pass
    except:
        pass

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    root.mainloop()


if __name__ == "__main__":
    main()
