# 视频压缩工具使用指南

## 快速开始

### 1. 安装依赖
双击运行 `run.bat`（Windows）或执行：
```bash
python install.py
```

### 2. 启动程序
```bash
python video_compressor.py
```

## 界面说明

### 文件选择区域
- **输入视频**: 点击"浏览"选择要压缩的视频文件
- **输出路径**: 设置压缩后的文件保存位置（会自动生成）

### 压缩设置区域
- **压缩质量**: CRF值，18-35范围
  - 18-20: 极高质量（文件较大）
  - 21-23: 高质量（推荐）
  - 24-28: 中等质量
  - 29-35: 低质量（文件较小）

- **输出分辨率**: 
  - 原始: 保持原分辨率
  - 1920x1080: 全高清
  - 1280x720: 高清
  - 854x480: 标清
  - 640x360: 低清

- **目标码率**:
  - 自动: 让FFmpeg自动选择
  - 500k-10000k: 手动设置码率

## 支持的格式

### 输入格式
- MP4, AVI, MOV, MKV, WMV, FLV, WEBM, M4V

### 输出格式
- 主要输出MP4格式（兼容性最好）
- 也支持AVI, MOV, MKV等

## 压缩建议

### 不同用途的推荐设置

#### 网络分享（平衡质量和大小）
- CRF: 23
- 分辨率: 1280x720
- 码率: 自动

#### 存储备份（高质量）
- CRF: 20
- 分辨率: 原始
- 码率: 自动

#### 快速预览（小文件）
- CRF: 28
- 分辨率: 854x480
- 码率: 1000k

#### 移动设备（省空间）
- CRF: 26
- 分辨率: 640x360
- 码率: 500k

## 常见问题

### Q: 提示"FFmpeg未找到"怎么办？
A: 需要安装FFmpeg并添加到系统PATH：
- Windows: 下载FFmpeg，解压后将bin目录添加到环境变量
- macOS: `brew install ffmpeg`
- Linux: `sudo apt install ffmpeg`

### Q: 压缩很慢怎么办？
A: 
- 降低输出分辨率
- 适当提高CRF值
- 确保电脑有足够的CPU和内存

### Q: 压缩后质量不满意？
A: 
- 降低CRF值（提高质量）
- 保持原始分辨率
- 增加目标码率

### Q: 文件大小没有明显减少？
A: 
- 原视频可能已经高度压缩
- 尝试降低分辨率
- 适当提高CRF值

### Q: 支持批量压缩吗？
A: 当前版本不支持，需要逐个处理文件

## 技术参数说明

### CRF (Constant Rate Factor)
- 恒定质量模式，推荐的压缩方式
- 值越小质量越高，文件越大
- 一般推荐18-28范围

### 码率 (Bitrate)
- 控制每秒传输的数据量
- 值越高质量越好，文件越大
- 可以与CRF结合使用

### 预设 (Preset)
- 程序默认使用"medium"预设
- 平衡压缩速度和效率
- 可选: ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow

## 注意事项

1. **备份原文件**: 压缩前建议备份原始视频
2. **磁盘空间**: 确保有足够空间存储输出文件
3. **处理时间**: 大文件压缩需要较长时间，请耐心等待
4. **系统资源**: 压缩过程会占用较多CPU资源
5. **格式兼容**: 输出的MP4格式兼容性最好

## 更新日志

### v1.0.0
- 基本的视频压缩功能
- 图形化界面
- 进度显示
- 多种压缩参数设置
