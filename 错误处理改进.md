# 视频压缩工具 - 错误处理改进

## 改进内容

### 🔧 修复的问题

#### 1. 重复压缩文件名问题
**问题**: 对已压缩的文件再次压缩时，文件名会变成 `_compressed_compressed.mp4`
**解决方案**: 
- 智能检测文件名是否已包含 `_compressed` 后缀
- 如果是重复压缩，使用 `_compressed_v2.mp4` 格式
- 避免文件名无限增长

#### 2. FFmpeg错误代码处理
**问题**: FFmpeg返回错误代码时，用户无法了解具体原因
**解决方案**:
- 添加错误代码解释函数
- 提供常见错误的中文说明
- 针对不同错误类型给出相应建议

#### 3. 视频时长获取失败
**问题**: 某些视频文件无法获取时长信息
**解决方案**:
- 多种方法尝试获取时长（ffmpeg-python + 直接命令）
- 查找视频流和格式信息中的时长数据
- 失败时使用简化进度显示

#### 4. 文件验证不足
**问题**: 没有验证输入文件是否为有效视频
**解决方案**:
- 添加文件存在性检查
- 使用ffprobe验证文件格式
- 检查文件可读性

### ✨ 新增功能

#### 1. 智能文件命名
```python
# 原始文件: video.mp4 → video_compressed.mp4
# 重复压缩: video_compressed.mp4 → video_compressed_v2.mp4
```

#### 2. 详细错误信息
- 文件权限问题检测
- 磁盘空间不足提示
- 文件格式不支持警告
- 编码参数错误说明

#### 3. 文件完整性检查
- 压缩前验证输入文件
- 压缩后验证输出文件
- 文件大小对比显示

#### 4. 增强的进度监控
- 主要方法：解析FFmpeg详细进度
- 备用方法：简化进度估算
- 自动切换机制

### 🛡️ 错误处理机制

#### 文件相关错误
- **文件不存在**: 提示用户重新选择文件
- **文件被占用**: 建议关闭相关程序
- **权限不足**: 提示以管理员身份运行
- **磁盘空间不足**: 建议清理磁盘空间

#### FFmpeg相关错误
- **编码器不支持**: 建议更新FFmpeg版本
- **参数错误**: 自动调整为默认参数
- **内存不足**: 建议降低分辨率或质量
- **格式不支持**: 提示支持的格式列表

#### 系统相关错误
- **编码问题**: 自动使用UTF-8编码
- **进程中断**: 提供重新开始选项
- **超时错误**: 增加处理时间限制

### 📊 错误代码说明

| 错误代码 | 含义 | 解决建议 |
|---------|------|----------|
| 1 | 一般错误 | 检查文件路径和权限 |
| 2 | 参数错误 | 重置为默认参数 |
| -1 | 程序中断 | 重新开始压缩 |
| -2 | 内存不足 | 降低质量设置 |
| -22 | 参数无效 | 检查输入格式 |

### 🔍 调试信息

程序现在提供更详细的日志信息：
- 输入文件大小和格式
- FFmpeg命令完整参数
- 处理进度和速度
- 错误详细描述
- 输出文件验证结果

### 💡 使用建议

#### 避免常见问题
1. **选择正确的文件格式**
   - 支持：MP4, AVI, MOV, MKV, WMV, FLV, WEBM
   - 推荐：MP4格式兼容性最好

2. **确保足够的磁盘空间**
   - 至少保留输入文件2倍大小的空间
   - 压缩过程中需要临时存储空间

3. **关闭相关程序**
   - 关闭正在播放该视频的播放器
   - 关闭可能占用文件的编辑软件

4. **选择合适的参数**
   - 高质量：CRF 18-20
   - 平衡：CRF 21-25（推荐）
   - 小文件：CRF 26-30

#### 故障排除步骤
1. 检查FFmpeg是否正确安装
2. 验证输入文件是否完整
3. 确认输出路径有写入权限
4. 尝试使用默认参数
5. 查看详细日志信息

### 🚀 性能优化

#### 处理速度优化
- 使用medium预设平衡速度和质量
- 避免过高的分辨率设置
- 合理选择CRF值

#### 内存使用优化
- 大文件分段处理
- 及时清理临时文件
- 监控系统资源使用

### 📝 更新日志

#### v1.2.0 (错误处理增强版)
- 🔧 修复重复压缩文件名问题
- 🔧 改进FFmpeg错误处理
- 🔧 增强文件验证机制
- 🔧 优化进度监控稳定性
- ✨ 添加详细错误说明
- ✨ 增加文件完整性检查

#### v1.1.0 (编码修复版)
- 🔧 修复Windows编码问题
- 🔧 改进进度显示稳定性

#### v1.0.0 (初始版本)
- ✨ 基础视频压缩功能
- ✨ 图形化用户界面

现在程序具有更强的错误处理能力和用户友好性，能够更好地处理各种异常情况并提供有用的反馈信息。
