# 视频压缩工具 - 编码问题修复

## 问题描述

在Windows系统上运行视频压缩工具时，可能会遇到以下错误：
```
进度监控错误: 'gbk' codec can't decode byte 0xba in position 65: illegal multibyte sequence
```

## 问题原因

这个问题是由于FFmpeg输出的进度信息包含了非标准编码字符，而Python在Windows系统上默认使用GBK编码导致的。当FFmpeg处理包含特殊字符的文件名或路径时，输出的进度信息可能包含无法用GBK编码解析的字节序列。

## 修复方案

### 1. 编码处理优化
- 在subprocess.Popen中明确指定UTF-8编码
- 添加`errors='ignore'`参数忽略编码错误
- 对进度解析添加异常处理

### 2. 进度监控改进
- 增加了单行解析的异常处理
- 添加了备用的简化进度监控
- 减少了日志输出频率，避免界面卡顿

### 3. 错误恢复机制
- 当详细进度监控失败时，自动切换到简化模式
- 确保压缩过程不会因进度显示问题而中断
- 保持用户界面的响应性

## 修复后的特性

### ✅ 编码兼容性
- 支持包含中文字符的文件名和路径
- 正确处理FFmpeg输出的各种编码格式
- 忽略无法解析的字符，继续正常工作

### ✅ 健壮的进度显示
- 主要进度监控：解析FFmpeg的详细进度信息
- 备用进度监控：简化的进度估算
- 自动切换：出错时无缝切换到备用方案

### ✅ 用户体验改进
- 减少了不必要的日志输出
- 更稳定的界面响应
- 更好的错误提示信息

## 使用建议

### 文件名建议
虽然程序现在可以处理中文文件名，但为了最佳兼容性，建议：
- 避免使用特殊符号（如：`<>:"|?*`）
- 文件路径不要过长
- 如果可能，使用英文文件名

### 系统环境
- 确保系统支持UTF-8编码
- Windows用户建议使用Windows 10或更新版本
- 确保FFmpeg版本较新（建议4.0+）

## 测试验证

修复后的程序已通过以下测试：
- ✅ 中文文件名处理
- ✅ 特殊字符路径处理
- ✅ 长时间压缩任务
- ✅ 进度监控稳定性
- ✅ 错误恢复机制

## 如果仍有问题

如果修复后仍然遇到编码相关问题，可以尝试：

1. **更新FFmpeg版本**
   ```bash
   # 下载最新版本的FFmpeg
   # 确保版本在4.0以上
   ```

2. **设置系统编码**
   - Windows: 控制面板 → 区域 → 管理 → 更改系统区域设置
   - 勾选"Beta版：使用Unicode UTF-8提供全球语言支持"

3. **使用英文路径**
   - 临时将视频文件移动到英文路径下进行处理
   - 处理完成后再移回原位置

4. **查看详细日志**
   - 程序会在日志区域显示详细的错误信息
   - 根据具体错误信息进行针对性处理

## 更新日志

### v1.1.0 (编码修复版)
- 🔧 修复了Windows系统下的编码问题
- 🔧 改进了进度监控的稳定性
- 🔧 添加了备用进度显示方案
- 🔧 优化了错误处理机制
- 🔧 减少了不必要的日志输出

### v1.0.0 (初始版本)
- ✨ 基本的视频压缩功能
- ✨ 图形化用户界面
- ✨ 多种压缩参数设置
